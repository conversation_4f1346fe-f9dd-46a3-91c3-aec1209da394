import{r as l,t as la,j as s,K as Yt,$ as Fe,U as he,S as ua}from"./app-GBhW32uO.js";import{c as ie,u as W,S as ve,a as T,b as da,d as fa,B as pa,e as Yn,A as ma}from"./app-logo-icon-9xTRfZp0.js";import{d as Ae,e as te,a as A,u as ut,c as pe,P as ce,f as ha,b as ga}from"./index-CLCtVWy1.js";import{P as I,d as Xn,R as va,r as xa}from"./index-GTcKX9PY.js";/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wa=[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]],ba=ie("BookOpen",wa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ya=[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]],Ca=ie("ChevronRight",ya);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ea=[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]],Ra=ie("ChevronsUpDown",Ea);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sa=[["path",{d:"M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z",key:"1kt360"}]],Ma=ie("Folder",Sa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Aa=[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]],Pa=ie("LayoutGrid",Aa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _a=[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]],Ta=ie("LogOut",_a);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]],Na=ie("PanelLeft",Da);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oa=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],ja=ie("Settings",Oa);/**
 * @license lucide-react v0.475.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ia=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],ka=ie("X",Ia),Pt=768;function qn(){const[e,t]=l.useState();return l.useEffect(()=>{const n=window.matchMedia(`(max-width: ${Pt-1}px)`),r=()=>{t(window.innerWidth<Pt)};return n.addEventListener("change",r),t(window.innerWidth<Pt),()=>n.removeEventListener("change",r)},[]),!!e}var La=la.useId||(()=>{}),Fa=0;function ge(e){const[t,n]=l.useState(La());return Ae(()=>{n(r=>r??String(Fa++))},[e]),e||(t?`radix-${t}`:"")}function $a(e,t=globalThis==null?void 0:globalThis.document){const n=te(e);l.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Ba="DismissableLayer",$t="dismissableLayer.update",Wa="dismissableLayer.pointerDownOutside",Ga="dismissableLayer.focusOutside",Sn,Zn=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),dt=l.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:a,onInteractOutside:i,onDismiss:c,...u}=e,d=l.useContext(Zn),[f,p]=l.useState(null),h=(f==null?void 0:f.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,g]=l.useState({}),v=W(t,R=>p(R)),m=Array.from(d.layers),[w]=[...d.layersWithOutsidePointerEventsDisabled].slice(-1),x=m.indexOf(w),b=f?m.indexOf(f):-1,y=d.layersWithOutsidePointerEventsDisabled.size>0,C=b>=x,E=Ua(R=>{const P=R.target,O=[...d.branches].some(D=>D.contains(P));!C||O||(o==null||o(R),i==null||i(R),R.defaultPrevented||c==null||c())},h),M=Va(R=>{const P=R.target;[...d.branches].some(D=>D.contains(P))||(a==null||a(R),i==null||i(R),R.defaultPrevented||c==null||c())},h);return $a(R=>{b===d.layers.size-1&&(r==null||r(R),!R.defaultPrevented&&c&&(R.preventDefault(),c()))},h),l.useEffect(()=>{if(f)return n&&(d.layersWithOutsidePointerEventsDisabled.size===0&&(Sn=h.body.style.pointerEvents,h.body.style.pointerEvents="none"),d.layersWithOutsidePointerEventsDisabled.add(f)),d.layers.add(f),Mn(),()=>{n&&d.layersWithOutsidePointerEventsDisabled.size===1&&(h.body.style.pointerEvents=Sn)}},[f,h,n,d]),l.useEffect(()=>()=>{f&&(d.layers.delete(f),d.layersWithOutsidePointerEventsDisabled.delete(f),Mn())},[f,d]),l.useEffect(()=>{const R=()=>g({});return document.addEventListener($t,R),()=>document.removeEventListener($t,R)},[]),s.jsx(I.div,{...u,ref:v,style:{pointerEvents:y?C?"auto":"none":void 0,...e.style},onFocusCapture:A(e.onFocusCapture,M.onFocusCapture),onBlurCapture:A(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:A(e.onPointerDownCapture,E.onPointerDownCapture)})});dt.displayName=Ba;var Ha="DismissableLayerBranch",Ka=l.forwardRef((e,t)=>{const n=l.useContext(Zn),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return n.branches.add(a),()=>{n.branches.delete(a)}},[n.branches]),s.jsx(I.div,{...e,ref:o})});Ka.displayName=Ha;function Ua(e,t=globalThis==null?void 0:globalThis.document){const n=te(e),r=l.useRef(!1),o=l.useRef(()=>{});return l.useEffect(()=>{const a=c=>{if(c.target&&!r.current){let u=function(){Qn(Wa,n,d,{discrete:!0})};const d={originalEvent:c};c.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=u,t.addEventListener("click",o.current,{once:!0})):u()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",a),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Va(e,t=globalThis==null?void 0:globalThis.document){const n=te(e),r=l.useRef(!1);return l.useEffect(()=>{const o=a=>{a.target&&!r.current&&Qn(Ga,n,{originalEvent:a},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function Mn(){const e=new CustomEvent($t);document.dispatchEvent(e)}function Qn(e,t,n,{discrete:r}){const o=n.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Xn(o,a):o.dispatchEvent(a)}var _t="focusScope.autoFocusOnMount",Tt="focusScope.autoFocusOnUnmount",An={bubbles:!1,cancelable:!0},za="FocusScope",Xt=l.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[c,u]=l.useState(null),d=te(o),f=te(a),p=l.useRef(null),h=W(t,m=>u(m)),g=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(r){let m=function(y){if(g.paused||!c)return;const C=y.target;c.contains(C)?p.current=C:le(p.current,{select:!0})},w=function(y){if(g.paused||!c)return;const C=y.relatedTarget;C!==null&&(c.contains(C)||le(p.current,{select:!0}))},x=function(y){if(document.activeElement===document.body)for(const E of y)E.removedNodes.length>0&&le(c)};document.addEventListener("focusin",m),document.addEventListener("focusout",w);const b=new MutationObserver(x);return c&&b.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",w),b.disconnect()}}},[r,c,g.paused]),l.useEffect(()=>{if(c){_n.add(g);const m=document.activeElement;if(!c.contains(m)){const x=new CustomEvent(_t,An);c.addEventListener(_t,d),c.dispatchEvent(x),x.defaultPrevented||(Ya(Ja(Jn(c)),{select:!0}),document.activeElement===m&&le(c))}return()=>{c.removeEventListener(_t,d),setTimeout(()=>{const x=new CustomEvent(Tt,An);c.addEventListener(Tt,f),c.dispatchEvent(x),x.defaultPrevented||le(m??document.body,{select:!0}),c.removeEventListener(Tt,f),_n.remove(g)},0)}}},[c,d,f,g]);const v=l.useCallback(m=>{if(!n&&!r||g.paused)return;const w=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,x=document.activeElement;if(w&&x){const b=m.currentTarget,[y,C]=Xa(b);y&&C?!m.shiftKey&&x===C?(m.preventDefault(),n&&le(y,{select:!0})):m.shiftKey&&x===y&&(m.preventDefault(),n&&le(C,{select:!0})):x===b&&m.preventDefault()}},[n,r,g.paused]);return s.jsx(I.div,{tabIndex:-1,...i,ref:h,onKeyDown:v})});Xt.displayName=za;function Ya(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(le(r,{select:t}),document.activeElement!==n)return}function Xa(e){const t=Jn(e),n=Pn(t,e),r=Pn(t.reverse(),e);return[n,r]}function Jn(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Pn(e,t){for(const n of e)if(!qa(n,{upTo:t}))return n}function qa(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function Za(e){return e instanceof HTMLInputElement&&"select"in e}function le(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&Za(e)&&t&&e.select()}}var _n=Qa();function Qa(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Tn(e,t),e.unshift(t)},remove(t){var n;e=Tn(e,t),(n=e[0])==null||n.resume()}}}function Tn(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function Ja(e){return e.filter(t=>t.tagName!=="A")}var es="Portal",ft=l.forwardRef((e,t)=>{var c;const{container:n,...r}=e,[o,a]=l.useState(!1);Ae(()=>a(!0),[]);const i=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return i?va.createPortal(s.jsx(I.div,{...r,ref:t}),i):null});ft.displayName=es;var Dt=0;function er(){l.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Dn()),document.body.insertAdjacentElement("beforeend",e[1]??Dn()),Dt++,()=>{Dt===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Dt--}},[])}function Dn(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var J=function(){return J=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(t[a]=n[a])}return t},J.apply(this,arguments)};function tr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function ts(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,a;r<o;r++)(a||!(r in t))&&(a||(a=Array.prototype.slice.call(t,0,r)),a[r]=t[r]);return e.concat(a||Array.prototype.slice.call(t))}var et="right-scroll-bar-position",tt="width-before-scroll-bar",ns="with-scroll-bars-hidden",rs="--removed-body-scroll-bar-size";function Nt(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function os(e,t){var n=l.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var as=typeof window<"u"?l.useLayoutEffect:l.useEffect,Nn=new WeakMap;function ss(e,t){var n=os(null,function(r){return e.forEach(function(o){return Nt(o,r)})});return as(function(){var r=Nn.get(n);if(r){var o=new Set(r),a=new Set(e),i=n.current;o.forEach(function(c){a.has(c)||Nt(c,null)}),a.forEach(function(c){o.has(c)||Nt(c,i)})}Nn.set(n,e)},[e]),n}function is(e){return e}function cs(e,t){t===void 0&&(t=is);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(a){var i=t(a,r);return n.push(i),function(){n=n.filter(function(c){return c!==i})}},assignSyncMedium:function(a){for(r=!0;n.length;){var i=n;n=[],i.forEach(a)}n={push:function(c){return a(c)},filter:function(){return n}}},assignMedium:function(a){r=!0;var i=[];if(n.length){var c=n;n=[],c.forEach(a),i=n}var u=function(){var f=i;i=[],f.forEach(a)},d=function(){return Promise.resolve().then(u)};d(),n={push:function(f){i.push(f),d()},filter:function(f){return i=i.filter(f),n}}}};return o}function ls(e){e===void 0&&(e={});var t=cs(null);return t.options=J({async:!0,ssr:!1},e),t}var nr=function(e){var t=e.sideCar,n=tr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return l.createElement(r,J({},n))};nr.isSideCarExport=!0;function us(e,t){return e.useMedium(t),nr}var rr=ls(),Ot=function(){},pt=l.forwardRef(function(e,t){var n=l.useRef(null),r=l.useState({onScrollCapture:Ot,onWheelCapture:Ot,onTouchMoveCapture:Ot}),o=r[0],a=r[1],i=e.forwardProps,c=e.children,u=e.className,d=e.removeScrollBar,f=e.enabled,p=e.shards,h=e.sideCar,g=e.noIsolation,v=e.inert,m=e.allowPinchZoom,w=e.as,x=w===void 0?"div":w,b=e.gapMode,y=tr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=h,E=ss([n,t]),M=J(J({},y),o);return l.createElement(l.Fragment,null,f&&l.createElement(C,{sideCar:rr,removeScrollBar:d,shards:p,noIsolation:g,inert:v,setCallbacks:a,allowPinchZoom:!!m,lockRef:n,gapMode:b}),i?l.cloneElement(l.Children.only(c),J(J({},M),{ref:E})):l.createElement(x,J({},M,{className:u,ref:E}),c))});pt.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};pt.classNames={fullWidth:tt,zeroRight:et};var ds=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function fs(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=ds();return t&&e.setAttribute("nonce",t),e}function ps(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function ms(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var hs=function(){var e=0,t=null;return{add:function(n){e==0&&(t=fs())&&(ps(t,n),ms(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},gs=function(){var e=hs();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},or=function(){var e=gs(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},vs={left:0,top:0,right:0,gap:0},jt=function(e){return parseInt(e||"",10)||0},xs=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[jt(n),jt(r),jt(o)]},ws=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return vs;var t=xs(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},bs=or(),Se="data-scroll-locked",ys=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(ns,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(c,"px ").concat(r,`;
  }
  body[`).concat(Se,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(a,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(c,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(et,` {
    right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(tt,` {
    margin-right: `).concat(c,"px ").concat(r,`;
  }
  
  .`).concat(et," .").concat(et,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(tt," .").concat(tt,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Se,`] {
    `).concat(rs,": ").concat(c,`px;
  }
`)},On=function(){var e=parseInt(document.body.getAttribute(Se)||"0",10);return isFinite(e)?e:0},Cs=function(){l.useEffect(function(){return document.body.setAttribute(Se,(On()+1).toString()),function(){var e=On()-1;e<=0?document.body.removeAttribute(Se):document.body.setAttribute(Se,e.toString())}},[])},Es=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Cs();var a=l.useMemo(function(){return ws(o)},[o]);return l.createElement(bs,{styles:ys(a,!t,o,n?"":"!important")})},Bt=!1;if(typeof window<"u")try{var Xe=Object.defineProperty({},"passive",{get:function(){return Bt=!0,!0}});window.addEventListener("test",Xe,Xe),window.removeEventListener("test",Xe,Xe)}catch{Bt=!1}var Ce=Bt?{passive:!1}:!1,Rs=function(e){return e.tagName==="TEXTAREA"},ar=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Rs(e)&&n[t]==="visible")},Ss=function(e){return ar(e,"overflowY")},Ms=function(e){return ar(e,"overflowX")},jn=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=sr(e,r);if(o){var a=ir(e,r),i=a[1],c=a[2];if(i>c)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},As=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Ps=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},sr=function(e,t){return e==="v"?Ss(t):Ms(t)},ir=function(e,t){return e==="v"?As(t):Ps(t)},_s=function(e,t){return e==="h"&&t==="rtl"?-1:1},Ts=function(e,t,n,r,o){var a=_s(e,window.getComputedStyle(t).direction),i=a*r,c=n.target,u=t.contains(c),d=!1,f=i>0,p=0,h=0;do{var g=ir(e,c),v=g[0],m=g[1],w=g[2],x=m-w-a*v;(v||x)&&sr(e,c)&&(p+=x,h+=v),c instanceof ShadowRoot?c=c.host:c=c.parentNode}while(!u&&c!==document.body||u&&(t.contains(c)||t===c));return(f&&Math.abs(p)<1||!f&&Math.abs(h)<1)&&(d=!0),d},qe=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},In=function(e){return[e.deltaX,e.deltaY]},kn=function(e){return e&&"current"in e?e.current:e},Ds=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Ns=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Os=0,Ee=[];function js(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(Os++)[0],a=l.useState(or)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=ts([e.lockRef.current],(e.shards||[]).map(kn),!0).filter(Boolean);return m.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var c=l.useCallback(function(m,w){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!i.current.allowPinchZoom;var x=qe(m),b=n.current,y="deltaX"in m?m.deltaX:b[0]-x[0],C="deltaY"in m?m.deltaY:b[1]-x[1],E,M=m.target,R=Math.abs(y)>Math.abs(C)?"h":"v";if("touches"in m&&R==="h"&&M.type==="range")return!1;var P=jn(R,M);if(!P)return!0;if(P?E=R:(E=R==="v"?"h":"v",P=jn(R,M)),!P)return!1;if(!r.current&&"changedTouches"in m&&(y||C)&&(r.current=E),!E)return!0;var O=r.current||E;return Ts(O,w,m,O==="h"?y:C)},[]),u=l.useCallback(function(m){var w=m;if(!(!Ee.length||Ee[Ee.length-1]!==a)){var x="deltaY"in w?In(w):qe(w),b=t.current.filter(function(E){return E.name===w.type&&(E.target===w.target||w.target===E.shadowParent)&&Ds(E.delta,x)})[0];if(b&&b.should){w.cancelable&&w.preventDefault();return}if(!b){var y=(i.current.shards||[]).map(kn).filter(Boolean).filter(function(E){return E.contains(w.target)}),C=y.length>0?c(w,y[0]):!i.current.noIsolation;C&&w.cancelable&&w.preventDefault()}}},[]),d=l.useCallback(function(m,w,x,b){var y={name:m,delta:w,target:x,should:b,shadowParent:Is(x)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(C){return C!==y})},1)},[]),f=l.useCallback(function(m){n.current=qe(m),r.current=void 0},[]),p=l.useCallback(function(m){d(m.type,In(m),m.target,c(m,e.lockRef.current))},[]),h=l.useCallback(function(m){d(m.type,qe(m),m.target,c(m,e.lockRef.current))},[]);l.useEffect(function(){return Ee.push(a),e.setCallbacks({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:h}),document.addEventListener("wheel",u,Ce),document.addEventListener("touchmove",u,Ce),document.addEventListener("touchstart",f,Ce),function(){Ee=Ee.filter(function(m){return m!==a}),document.removeEventListener("wheel",u,Ce),document.removeEventListener("touchmove",u,Ce),document.removeEventListener("touchstart",f,Ce)}},[]);var g=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(a,{styles:Ns(o)}):null,g?l.createElement(Es,{gapMode:e.gapMode}):null)}function Is(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const ks=us(rr,js);var qt=l.forwardRef(function(e,t){return l.createElement(pt,J({},e,{ref:t,sideCar:ks}))});qt.classNames=pt.classNames;var Ls=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},Re=new WeakMap,Ze=new WeakMap,Qe={},It=0,cr=function(e){return e&&(e.host||cr(e.parentNode))},Fs=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=cr(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},$s=function(e,t,n,r){var o=Fs(t,Array.isArray(e)?e:[e]);Qe[n]||(Qe[n]=new WeakMap);var a=Qe[n],i=[],c=new Set,u=new Set(o),d=function(p){!p||c.has(p)||(c.add(p),d(p.parentNode))};o.forEach(d);var f=function(p){!p||u.has(p)||Array.prototype.forEach.call(p.children,function(h){if(c.has(h))f(h);else try{var g=h.getAttribute(r),v=g!==null&&g!=="false",m=(Re.get(h)||0)+1,w=(a.get(h)||0)+1;Re.set(h,m),a.set(h,w),i.push(h),m===1&&v&&Ze.set(h,!0),w===1&&h.setAttribute(n,"true"),v||h.setAttribute(r,"true")}catch(x){console.error("aria-hidden: cannot operate on ",h,x)}})};return f(t),c.clear(),It++,function(){i.forEach(function(p){var h=Re.get(p)-1,g=a.get(p)-1;Re.set(p,h),a.set(p,g),h||(Ze.has(p)||p.removeAttribute(r),Ze.delete(p)),g||p.removeAttribute(n)}),It--,It||(Re=new WeakMap,Re=new WeakMap,Ze=new WeakMap,Qe={})}},lr=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=Ls(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),$s(r,o,n,"aria-hidden")):function(){return null}},Zt="Dialog",[ur,gd]=pe(Zt),[Bs,Z]=ur(Zt),dr=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,c=l.useRef(null),u=l.useRef(null),[d=!1,f]=ut({prop:r,defaultProp:o,onChange:a});return s.jsx(Bs,{scope:t,triggerRef:c,contentRef:u,contentId:ge(),titleId:ge(),descriptionId:ge(),open:d,onOpenChange:f,onOpenToggle:l.useCallback(()=>f(p=>!p),[f]),modal:i,children:n})};dr.displayName=Zt;var fr="DialogTrigger",pr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Z(fr,n),a=W(t,o.triggerRef);return s.jsx(I.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":en(o.open),...r,ref:a,onClick:A(e.onClick,o.onOpenToggle)})});pr.displayName=fr;var Qt="DialogPortal",[Ws,mr]=ur(Qt,{forceMount:void 0}),hr=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=Z(Qt,t);return s.jsx(Ws,{scope:t,forceMount:n,children:l.Children.map(r,i=>s.jsx(ce,{present:n||a.open,children:s.jsx(ft,{asChild:!0,container:o,children:i})}))})};hr.displayName=Qt;var rt="DialogOverlay",gr=l.forwardRef((e,t)=>{const n=mr(rt,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Z(rt,e.__scopeDialog);return a.modal?s.jsx(ce,{present:r||a.open,children:s.jsx(Gs,{...o,ref:t})}):null});gr.displayName=rt;var Gs=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Z(rt,n);return s.jsx(qt,{as:ve,allowPinchZoom:!0,shards:[o.contentRef],children:s.jsx(I.div,{"data-state":en(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),xe="DialogContent",vr=l.forwardRef((e,t)=>{const n=mr(xe,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=Z(xe,e.__scopeDialog);return s.jsx(ce,{present:r||a.open,children:a.modal?s.jsx(Hs,{...o,ref:t}):s.jsx(Ks,{...o,ref:t})})});vr.displayName=xe;var Hs=l.forwardRef((e,t)=>{const n=Z(xe,e.__scopeDialog),r=l.useRef(null),o=W(t,n.contentRef,r);return l.useEffect(()=>{const a=r.current;if(a)return lr(a)},[]),s.jsx(xr,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:A(e.onCloseAutoFocus,a=>{var i;a.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:A(e.onPointerDownOutside,a=>{const i=a.detail.originalEvent,c=i.button===0&&i.ctrlKey===!0;(i.button===2||c)&&a.preventDefault()}),onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault())})}),Ks=l.forwardRef((e,t)=>{const n=Z(xe,e.__scopeDialog),r=l.useRef(!1),o=l.useRef(!1);return s.jsx(xr,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:a=>{var i,c;(i=e.onCloseAutoFocus)==null||i.call(e,a),a.defaultPrevented||(r.current||(c=n.triggerRef.current)==null||c.focus(),a.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:a=>{var u,d;(u=e.onInteractOutside)==null||u.call(e,a),a.defaultPrevented||(r.current=!0,a.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=a.target;((d=n.triggerRef.current)==null?void 0:d.contains(i))&&a.preventDefault(),a.detail.originalEvent.type==="focusin"&&o.current&&a.preventDefault()}})}),xr=l.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,c=Z(xe,n),u=l.useRef(null),d=W(t,u);return er(),s.jsxs(s.Fragment,{children:[s.jsx(Xt,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:s.jsx(dt,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":en(c.open),...i,ref:d,onDismiss:()=>c.onOpenChange(!1)})}),s.jsxs(s.Fragment,{children:[s.jsx(Us,{titleId:c.titleId}),s.jsx(zs,{contentRef:u,descriptionId:c.descriptionId})]})]})}),Jt="DialogTitle",wr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Z(Jt,n);return s.jsx(I.h2,{id:o.titleId,...r,ref:t})});wr.displayName=Jt;var br="DialogDescription",yr=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Z(br,n);return s.jsx(I.p,{id:o.descriptionId,...r,ref:t})});yr.displayName=br;var Cr="DialogClose",Er=l.forwardRef((e,t)=>{const{__scopeDialog:n,...r}=e,o=Z(Cr,n);return s.jsx(I.button,{type:"button",...r,ref:t,onClick:A(e.onClick,()=>o.onOpenChange(!1))})});Er.displayName=Cr;function en(e){return e?"open":"closed"}var Rr="DialogTitleWarning",[vd,Sr]=ha(Rr,{contentName:xe,titleName:Jt,docsSlug:"dialog"}),Us=({titleId:e})=>{const t=Sr(Rr),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return l.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},Vs="DialogDescriptionWarning",zs=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Sr(Vs).contentName}}.`;return l.useEffect(()=>{var a;const o=(a=e.current)==null?void 0:a.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},Ys=dr,xd=pr,Xs=hr,qs=gr,Zs=vr,Qs=wr,Js=yr,ei=Er;function ti({...e}){return s.jsx(Ys,{"data-slot":"sheet",...e})}function ni({...e}){return s.jsx(Xs,{"data-slot":"sheet-portal",...e})}function ri({className:e,...t}){return s.jsx(qs,{"data-slot":"sheet-overlay",className:T("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/80",e),...t})}function oi({className:e,children:t,side:n="right",...r}){return s.jsxs(ni,{children:[s.jsx(ri,{}),s.jsxs(Zs,{"data-slot":"sheet-content",className:T("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",n==="right"&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",n==="left"&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",n==="top"&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",n==="bottom"&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",e),...r,children:[t,s.jsxs(ei,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[s.jsx(ka,{className:"size-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function ai({className:e,...t}){return s.jsx("div",{"data-slot":"sheet-header",className:T("flex flex-col gap-1.5 p-4",e),...t})}function si({className:e,...t}){return s.jsx(Qs,{"data-slot":"sheet-title",className:T("text-foreground font-semibold",e),...t})}function ii({className:e,...t}){return s.jsx(Js,{"data-slot":"sheet-description",className:T("text-muted-foreground text-sm",e),...t})}const ci=["top","right","bottom","left"],ue=Math.min,U=Math.max,ot=Math.round,Je=Math.floor,ee=e=>({x:e,y:e}),li={left:"right",right:"left",bottom:"top",top:"bottom"},ui={start:"end",end:"start"};function Wt(e,t,n){return U(e,ue(t,n))}function ae(e,t){return typeof e=="function"?e(t):e}function se(e){return e.split("-")[0]}function Te(e){return e.split("-")[1]}function tn(e){return e==="x"?"y":"x"}function nn(e){return e==="y"?"height":"width"}function de(e){return["top","bottom"].includes(se(e))?"y":"x"}function rn(e){return tn(de(e))}function di(e,t,n){n===void 0&&(n=!1);const r=Te(e),o=rn(e),a=nn(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=at(i)),[i,at(i)]}function fi(e){const t=at(e);return[Gt(e),t,Gt(t)]}function Gt(e){return e.replace(/start|end/g,t=>ui[t])}function pi(e,t,n){const r=["left","right"],o=["right","left"],a=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?a:i;default:return[]}}function mi(e,t,n,r){const o=Te(e);let a=pi(se(e),n==="start",r);return o&&(a=a.map(i=>i+"-"+o),t&&(a=a.concat(a.map(Gt)))),a}function at(e){return e.replace(/left|right|bottom|top/g,t=>li[t])}function hi(e){return{top:0,right:0,bottom:0,left:0,...e}}function Mr(e){return typeof e!="number"?hi(e):{top:e,right:e,bottom:e,left:e}}function st(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function Ln(e,t,n){let{reference:r,floating:o}=e;const a=de(t),i=rn(t),c=nn(i),u=se(t),d=a==="y",f=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,h=r[c]/2-o[c]/2;let g;switch(u){case"top":g={x:f,y:r.y-o.height};break;case"bottom":g={x:f,y:r.y+r.height};break;case"right":g={x:r.x+r.width,y:p};break;case"left":g={x:r.x-o.width,y:p};break;default:g={x:r.x,y:r.y}}switch(Te(t)){case"start":g[i]-=h*(n&&d?-1:1);break;case"end":g[i]+=h*(n&&d?-1:1);break}return g}const gi=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,c=a.filter(Boolean),u=await(i.isRTL==null?void 0:i.isRTL(t));let d=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:f,y:p}=Ln(d,r,u),h=r,g={},v=0;for(let m=0;m<c.length;m++){const{name:w,fn:x}=c[m],{x:b,y,data:C,reset:E}=await x({x:f,y:p,initialPlacement:r,placement:h,strategy:o,middlewareData:g,rects:d,platform:i,elements:{reference:e,floating:t}});f=b??f,p=y??p,g={...g,[w]:{...g[w],...C}},E&&v<=50&&(v++,typeof E=="object"&&(E.placement&&(h=E.placement),E.rects&&(d=E.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):E.rects),{x:f,y:p}=Ln(d,h,u)),m=-1)}return{x:f,y:p,placement:h,strategy:o,middlewareData:g}};async function $e(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:a,rects:i,elements:c,strategy:u}=e,{boundary:d="clippingAncestors",rootBoundary:f="viewport",elementContext:p="floating",altBoundary:h=!1,padding:g=0}=ae(t,e),v=Mr(g),w=c[h?p==="floating"?"reference":"floating":p],x=st(await a.getClippingRect({element:(n=await(a.isElement==null?void 0:a.isElement(w)))==null||n?w:w.contextElement||await(a.getDocumentElement==null?void 0:a.getDocumentElement(c.floating)),boundary:d,rootBoundary:f,strategy:u})),b=p==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(a.getOffsetParent==null?void 0:a.getOffsetParent(c.floating)),C=await(a.isElement==null?void 0:a.isElement(y))?await(a.getScale==null?void 0:a.getScale(y))||{x:1,y:1}:{x:1,y:1},E=st(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:c,rect:b,offsetParent:y,strategy:u}):b);return{top:(x.top-E.top+v.top)/C.y,bottom:(E.bottom-x.bottom+v.bottom)/C.y,left:(x.left-E.left+v.left)/C.x,right:(E.right-x.right+v.right)/C.x}}const vi=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:c,middlewareData:u}=t,{element:d,padding:f=0}=ae(e,t)||{};if(d==null)return{};const p=Mr(f),h={x:n,y:r},g=rn(o),v=nn(g),m=await i.getDimensions(d),w=g==="y",x=w?"top":"left",b=w?"bottom":"right",y=w?"clientHeight":"clientWidth",C=a.reference[v]+a.reference[g]-h[g]-a.floating[v],E=h[g]-a.reference[g],M=await(i.getOffsetParent==null?void 0:i.getOffsetParent(d));let R=M?M[y]:0;(!R||!await(i.isElement==null?void 0:i.isElement(M)))&&(R=c.floating[y]||a.floating[v]);const P=C/2-E/2,O=R/2-m[v]/2-1,D=ue(p[x],O),F=ue(p[b],O),$=D,k=R-m[v]-F,j=R/2-m[v]/2+P,G=Wt($,j,k),N=!u.arrow&&Te(o)!=null&&j!==G&&a.reference[v]/2-(j<$?D:F)-m[v]/2<0,B=N?j<$?j-$:j-k:0;return{[g]:h[g]+B,data:{[g]:G,centerOffset:j-G-B,...N&&{alignmentOffset:B}},reset:N}}}),xi=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:c,platform:u,elements:d}=t,{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:h,fallbackStrategy:g="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:m=!0,...w}=ae(e,t);if((n=a.arrow)!=null&&n.alignmentOffset)return{};const x=se(o),b=de(c),y=se(c)===c,C=await(u.isRTL==null?void 0:u.isRTL(d.floating)),E=h||(y||!m?[at(c)]:fi(c)),M=v!=="none";!h&&M&&E.push(...mi(c,m,v,C));const R=[c,...E],P=await $e(t,w),O=[];let D=((r=a.flip)==null?void 0:r.overflows)||[];if(f&&O.push(P[x]),p){const j=di(o,i,C);O.push(P[j[0]],P[j[1]])}if(D=[...D,{placement:o,overflows:O}],!O.every(j=>j<=0)){var F,$;const j=(((F=a.flip)==null?void 0:F.index)||0)+1,G=R[j];if(G)return{data:{index:j,overflows:D},reset:{placement:G}};let N=($=D.filter(B=>B.overflows[0]<=0).sort((B,_)=>B.overflows[1]-_.overflows[1])[0])==null?void 0:$.placement;if(!N)switch(g){case"bestFit":{var k;const B=(k=D.filter(_=>{if(M){const S=de(_.placement);return S===b||S==="y"}return!0}).map(_=>[_.placement,_.overflows.filter(S=>S>0).reduce((S,L)=>S+L,0)]).sort((_,S)=>_[1]-S[1])[0])==null?void 0:k[0];B&&(N=B);break}case"initialPlacement":N=c;break}if(o!==N)return{reset:{placement:N}}}return{}}}};function Fn(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function $n(e){return ci.some(t=>e[t]>=0)}const wi=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ae(e,t);switch(r){case"referenceHidden":{const a=await $e(t,{...o,elementContext:"reference"}),i=Fn(a,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:$n(i)}}}case"escaped":{const a=await $e(t,{...o,altBoundary:!0}),i=Fn(a,n.floating);return{data:{escapedOffsets:i,escaped:$n(i)}}}default:return{}}}}};async function bi(e,t){const{placement:n,platform:r,elements:o}=e,a=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=se(n),c=Te(n),u=de(n)==="y",d=["left","top"].includes(i)?-1:1,f=a&&u?-1:1,p=ae(t,e);let{mainAxis:h,crossAxis:g,alignmentAxis:v}=typeof p=="number"?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return c&&typeof v=="number"&&(g=c==="end"?v*-1:v),u?{x:g*f,y:h*d}:{x:h*d,y:g*f}}const yi=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:c}=t,u=await bi(t,e);return i===((n=c.offset)==null?void 0:n.placement)&&(r=c.arrow)!=null&&r.alignmentOffset?{}:{x:o+u.x,y:a+u.y,data:{...u,placement:i}}}}},Ci=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:a=!0,crossAxis:i=!1,limiter:c={fn:w=>{let{x,y:b}=w;return{x,y:b}}},...u}=ae(e,t),d={x:n,y:r},f=await $e(t,u),p=de(se(o)),h=tn(p);let g=d[h],v=d[p];if(a){const w=h==="y"?"top":"left",x=h==="y"?"bottom":"right",b=g+f[w],y=g-f[x];g=Wt(b,g,y)}if(i){const w=p==="y"?"top":"left",x=p==="y"?"bottom":"right",b=v+f[w],y=v-f[x];v=Wt(b,v,y)}const m=c.fn({...t,[h]:g,[p]:v});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[h]:a,[p]:i}}}}}},Ei=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:i}=t,{offset:c=0,mainAxis:u=!0,crossAxis:d=!0}=ae(e,t),f={x:n,y:r},p=de(o),h=tn(p);let g=f[h],v=f[p];const m=ae(c,t),w=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){const y=h==="y"?"height":"width",C=a.reference[h]-a.floating[y]+w.mainAxis,E=a.reference[h]+a.reference[y]-w.mainAxis;g<C?g=C:g>E&&(g=E)}if(d){var x,b;const y=h==="y"?"width":"height",C=["top","left"].includes(se(o)),E=a.reference[p]-a.floating[y]+(C&&((x=i.offset)==null?void 0:x[p])||0)+(C?0:w.crossAxis),M=a.reference[p]+a.reference[y]+(C?0:((b=i.offset)==null?void 0:b[p])||0)-(C?w.crossAxis:0);v<E?v=E:v>M&&(v=M)}return{[h]:g,[p]:v}}}},Ri=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:i,elements:c}=t,{apply:u=()=>{},...d}=ae(e,t),f=await $e(t,d),p=se(o),h=Te(o),g=de(o)==="y",{width:v,height:m}=a.floating;let w,x;p==="top"||p==="bottom"?(w=p,x=h===(await(i.isRTL==null?void 0:i.isRTL(c.floating))?"start":"end")?"left":"right"):(x=p,w=h==="end"?"top":"bottom");const b=m-f.top-f.bottom,y=v-f.left-f.right,C=ue(m-f[w],b),E=ue(v-f[x],y),M=!t.middlewareData.shift;let R=C,P=E;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(R=b),M&&!h){const D=U(f.left,0),F=U(f.right,0),$=U(f.top,0),k=U(f.bottom,0);g?P=v-2*(D!==0||F!==0?D+F:U(f.left,f.right)):R=m-2*($!==0||k!==0?$+k:U(f.top,f.bottom))}await u({...t,availableWidth:P,availableHeight:R});const O=await i.getDimensions(c.floating);return v!==O.width||m!==O.height?{reset:{rects:!0}}:{}}}};function mt(){return typeof window<"u"}function De(e){return Ar(e)?(e.nodeName||"").toLowerCase():"#document"}function V(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function re(e){var t;return(t=(Ar(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ar(e){return mt()?e instanceof Node||e instanceof V(e).Node:!1}function X(e){return mt()?e instanceof Element||e instanceof V(e).Element:!1}function ne(e){return mt()?e instanceof HTMLElement||e instanceof V(e).HTMLElement:!1}function Bn(e){return!mt()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof V(e).ShadowRoot}function He(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=q(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function Si(e){return["table","td","th"].includes(De(e))}function ht(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function on(e){const t=an(),n=X(e)?q(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Mi(e){let t=fe(e);for(;ne(t)&&!Pe(t);){if(on(t))return t;if(ht(t))return null;t=fe(t)}return null}function an(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Pe(e){return["html","body","#document"].includes(De(e))}function q(e){return V(e).getComputedStyle(e)}function gt(e){return X(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function fe(e){if(De(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Bn(e)&&e.host||re(e);return Bn(t)?t.host:t}function Pr(e){const t=fe(e);return Pe(t)?e.ownerDocument?e.ownerDocument.body:e.body:ne(t)&&He(t)?t:Pr(t)}function Be(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Pr(e),a=o===((r=e.ownerDocument)==null?void 0:r.body),i=V(o);if(a){const c=Ht(i);return t.concat(i,i.visualViewport||[],He(o)?o:[],c&&n?Be(c):[])}return t.concat(o,Be(o,[],n))}function Ht(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function _r(e){const t=q(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=ne(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,c=ot(n)!==a||ot(r)!==i;return c&&(n=a,r=i),{width:n,height:r,$:c}}function sn(e){return X(e)?e:e.contextElement}function Me(e){const t=sn(e);if(!ne(t))return ee(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=_r(t);let i=(a?ot(n.width):n.width)/r,c=(a?ot(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!c||!Number.isFinite(c))&&(c=1),{x:i,y:c}}const Ai=ee(0);function Tr(e){const t=V(e);return!an()||!t.visualViewport?Ai:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Pi(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==V(e)?!1:t}function we(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),a=sn(e);let i=ee(1);t&&(r?X(r)&&(i=Me(r)):i=Me(e));const c=Pi(a,n,r)?Tr(a):ee(0);let u=(o.left+c.x)/i.x,d=(o.top+c.y)/i.y,f=o.width/i.x,p=o.height/i.y;if(a){const h=V(a),g=r&&X(r)?V(r):r;let v=h,m=Ht(v);for(;m&&r&&g!==v;){const w=Me(m),x=m.getBoundingClientRect(),b=q(m),y=x.left+(m.clientLeft+parseFloat(b.paddingLeft))*w.x,C=x.top+(m.clientTop+parseFloat(b.paddingTop))*w.y;u*=w.x,d*=w.y,f*=w.x,p*=w.y,u+=y,d+=C,v=V(m),m=Ht(v)}}return st({width:f,height:p,x:u,y:d})}function cn(e,t){const n=gt(e).scrollLeft;return t?t.left+n:we(re(e)).left+n}function Dr(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:cn(e,r)),a=r.top+t.scrollTop;return{x:o,y:a}}function _i(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a=o==="fixed",i=re(r),c=t?ht(t.floating):!1;if(r===i||c&&a)return n;let u={scrollLeft:0,scrollTop:0},d=ee(1);const f=ee(0),p=ne(r);if((p||!p&&!a)&&((De(r)!=="body"||He(i))&&(u=gt(r)),ne(r))){const g=we(r);d=Me(r),f.x=g.x+r.clientLeft,f.y=g.y+r.clientTop}const h=i&&!p&&!a?Dr(i,u,!0):ee(0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-u.scrollLeft*d.x+f.x+h.x,y:n.y*d.y-u.scrollTop*d.y+f.y+h.y}}function Ti(e){return Array.from(e.getClientRects())}function Di(e){const t=re(e),n=gt(e),r=e.ownerDocument.body,o=U(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=U(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+cn(e);const c=-n.scrollTop;return q(r).direction==="rtl"&&(i+=U(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:c}}function Ni(e,t){const n=V(e),r=re(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,c=0,u=0;if(o){a=o.width,i=o.height;const d=an();(!d||d&&t==="fixed")&&(c=o.offsetLeft,u=o.offsetTop)}return{width:a,height:i,x:c,y:u}}function Oi(e,t){const n=we(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=ne(e)?Me(e):ee(1),i=e.clientWidth*a.x,c=e.clientHeight*a.y,u=o*a.x,d=r*a.y;return{width:i,height:c,x:u,y:d}}function Wn(e,t,n){let r;if(t==="viewport")r=Ni(e,n);else if(t==="document")r=Di(re(e));else if(X(t))r=Oi(t,n);else{const o=Tr(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return st(r)}function Nr(e,t){const n=fe(e);return n===t||!X(n)||Pe(n)?!1:q(n).position==="fixed"||Nr(n,t)}function ji(e,t){const n=t.get(e);if(n)return n;let r=Be(e,[],!1).filter(c=>X(c)&&De(c)!=="body"),o=null;const a=q(e).position==="fixed";let i=a?fe(e):e;for(;X(i)&&!Pe(i);){const c=q(i),u=on(i);!u&&c.position==="fixed"&&(o=null),(a?!u&&!o:!u&&c.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||He(i)&&!u&&Nr(e,i))?r=r.filter(f=>f!==i):o=c,i=fe(i)}return t.set(e,r),r}function Ii(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?ht(t)?[]:ji(t,this._c):[].concat(n),r],c=i[0],u=i.reduce((d,f)=>{const p=Wn(t,f,o);return d.top=U(p.top,d.top),d.right=ue(p.right,d.right),d.bottom=ue(p.bottom,d.bottom),d.left=U(p.left,d.left),d},Wn(t,c,o));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}}function ki(e){const{width:t,height:n}=_r(e);return{width:t,height:n}}function Li(e,t,n){const r=ne(t),o=re(t),a=n==="fixed",i=we(e,!0,a,t);let c={scrollLeft:0,scrollTop:0};const u=ee(0);if(r||!r&&!a)if((De(t)!=="body"||He(o))&&(c=gt(t)),r){const h=we(t,!0,a,t);u.x=h.x+t.clientLeft,u.y=h.y+t.clientTop}else o&&(u.x=cn(o));const d=o&&!r&&!a?Dr(o,c):ee(0),f=i.left+c.scrollLeft-u.x-d.x,p=i.top+c.scrollTop-u.y-d.y;return{x:f,y:p,width:i.width,height:i.height}}function kt(e){return q(e).position==="static"}function Gn(e,t){if(!ne(e)||q(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return re(e)===n&&(n=n.ownerDocument.body),n}function Or(e,t){const n=V(e);if(ht(e))return n;if(!ne(e)){let o=fe(e);for(;o&&!Pe(o);){if(X(o)&&!kt(o))return o;o=fe(o)}return n}let r=Gn(e,t);for(;r&&Si(r)&&kt(r);)r=Gn(r,t);return r&&Pe(r)&&kt(r)&&!on(r)?n:r||Mi(e)||n}const Fi=async function(e){const t=this.getOffsetParent||Or,n=this.getDimensions,r=await n(e.floating);return{reference:Li(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function $i(e){return q(e).direction==="rtl"}const Bi={convertOffsetParentRelativeRectToViewportRelativeRect:_i,getDocumentElement:re,getClippingRect:Ii,getOffsetParent:Or,getElementRects:Fi,getClientRects:Ti,getDimensions:ki,getScale:Me,isElement:X,isRTL:$i};function jr(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Wi(e,t){let n=null,r;const o=re(e);function a(){var c;clearTimeout(r),(c=n)==null||c.disconnect(),n=null}function i(c,u){c===void 0&&(c=!1),u===void 0&&(u=1),a();const d=e.getBoundingClientRect(),{left:f,top:p,width:h,height:g}=d;if(c||t(),!h||!g)return;const v=Je(p),m=Je(o.clientWidth-(f+h)),w=Je(o.clientHeight-(p+g)),x=Je(f),y={rootMargin:-v+"px "+-m+"px "+-w+"px "+-x+"px",threshold:U(0,ue(1,u))||1};let C=!0;function E(M){const R=M[0].intersectionRatio;if(R!==u){if(!C)return i();R?i(!1,R):r=setTimeout(()=>{i(!1,1e-7)},1e3)}R===1&&!jr(d,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(E,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(E,y)}n.observe(e)}return i(!0),a}function Gi(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:c=typeof IntersectionObserver=="function",animationFrame:u=!1}=r,d=sn(e),f=o||a?[...d?Be(d):[],...Be(t)]:[];f.forEach(x=>{o&&x.addEventListener("scroll",n,{passive:!0}),a&&x.addEventListener("resize",n)});const p=d&&c?Wi(d,n):null;let h=-1,g=null;i&&(g=new ResizeObserver(x=>{let[b]=x;b&&b.target===d&&g&&(g.unobserve(t),cancelAnimationFrame(h),h=requestAnimationFrame(()=>{var y;(y=g)==null||y.observe(t)})),n()}),d&&!u&&g.observe(d),g.observe(t));let v,m=u?we(e):null;u&&w();function w(){const x=we(e);m&&!jr(m,x)&&n(),m=x,v=requestAnimationFrame(w)}return n(),()=>{var x;f.forEach(b=>{o&&b.removeEventListener("scroll",n),a&&b.removeEventListener("resize",n)}),p==null||p(),(x=g)==null||x.disconnect(),g=null,u&&cancelAnimationFrame(v)}}const Hi=yi,Ki=Ci,Ui=xi,Vi=Ri,zi=wi,Hn=vi,Yi=Ei,Xi=(e,t,n)=>{const r=new Map,o={platform:Bi,...n},a={...o.platform,_c:r};return gi(e,t,{...o,platform:a})};var nt=typeof document<"u"?l.useLayoutEffect:l.useEffect;function it(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!it(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const a=o[r];if(!(a==="_owner"&&e.$$typeof)&&!it(e[a],t[a]))return!1}return!0}return e!==e&&t!==t}function Ir(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Kn(e,t){const n=Ir(e);return Math.round(t*n)/n}function Lt(e){const t=l.useRef(e);return nt(()=>{t.current=e}),t}function qi(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:a,floating:i}={},transform:c=!0,whileElementsMounted:u,open:d}=e,[f,p]=l.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[h,g]=l.useState(r);it(h,r)||g(r);const[v,m]=l.useState(null),[w,x]=l.useState(null),b=l.useCallback(_=>{_!==M.current&&(M.current=_,m(_))},[]),y=l.useCallback(_=>{_!==R.current&&(R.current=_,x(_))},[]),C=a||v,E=i||w,M=l.useRef(null),R=l.useRef(null),P=l.useRef(f),O=u!=null,D=Lt(u),F=Lt(o),$=Lt(d),k=l.useCallback(()=>{if(!M.current||!R.current)return;const _={placement:t,strategy:n,middleware:h};F.current&&(_.platform=F.current),Xi(M.current,R.current,_).then(S=>{const L={...S,isPositioned:$.current!==!1};j.current&&!it(P.current,L)&&(P.current=L,xa.flushSync(()=>{p(L)}))})},[h,t,n,F,$]);nt(()=>{d===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,p(_=>({..._,isPositioned:!1})))},[d]);const j=l.useRef(!1);nt(()=>(j.current=!0,()=>{j.current=!1}),[]),nt(()=>{if(C&&(M.current=C),E&&(R.current=E),C&&E){if(D.current)return D.current(C,E,k);k()}},[C,E,k,D,O]);const G=l.useMemo(()=>({reference:M,floating:R,setReference:b,setFloating:y}),[b,y]),N=l.useMemo(()=>({reference:C,floating:E}),[C,E]),B=l.useMemo(()=>{const _={position:n,left:0,top:0};if(!N.floating)return _;const S=Kn(N.floating,f.x),L=Kn(N.floating,f.y);return c?{..._,transform:"translate("+S+"px, "+L+"px)",...Ir(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:S,top:L}},[n,c,N.floating,f.x,f.y]);return l.useMemo(()=>({...f,update:k,refs:G,elements:N,floatingStyles:B}),[f,k,G,N,B])}const Zi=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Hn({element:r.current,padding:o}).fn(n):{}:r?Hn({element:r,padding:o}).fn(n):{}}}},Qi=(e,t)=>({...Hi(e),options:[e,t]}),Ji=(e,t)=>({...Ki(e),options:[e,t]}),ec=(e,t)=>({...Yi(e),options:[e,t]}),tc=(e,t)=>({...Ui(e),options:[e,t]}),nc=(e,t)=>({...Vi(e),options:[e,t]}),rc=(e,t)=>({...zi(e),options:[e,t]}),oc=(e,t)=>({...Zi(e),options:[e,t]});var ac="Arrow",kr=l.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...a}=e;return s.jsx(I.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:s.jsx("polygon",{points:"0,0 30,0 15,10"})})});kr.displayName=ac;var sc=kr,ln="Popper",[Lr,vt]=pe(ln),[ic,Fr]=Lr(ln),$r=e=>{const{__scopePopper:t,children:n}=e,[r,o]=l.useState(null);return s.jsx(ic,{scope:t,anchor:r,onAnchorChange:o,children:n})};$r.displayName=ln;var Br="PopperAnchor",Wr=l.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,a=Fr(Br,n),i=l.useRef(null),c=W(t,i);return l.useEffect(()=>{a.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:s.jsx(I.div,{...o,ref:c})});Wr.displayName=Br;var un="PopperContent",[cc,lc]=Lr(un),Gr=l.forwardRef((e,t)=>{var oe,je,z,Ie,Cn,En;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:a="center",alignOffset:i=0,arrowPadding:c=0,avoidCollisions:u=!0,collisionBoundary:d=[],collisionPadding:f=0,sticky:p="partial",hideWhenDetached:h=!1,updatePositionStrategy:g="optimized",onPlaced:v,...m}=e,w=Fr(un,n),[x,b]=l.useState(null),y=W(t,ke=>b(ke)),[C,E]=l.useState(null),M=ga(C),R=(M==null?void 0:M.width)??0,P=(M==null?void 0:M.height)??0,O=r+(a!=="center"?"-"+a:""),D=typeof f=="number"?f:{top:0,right:0,bottom:0,left:0,...f},F=Array.isArray(d)?d:[d],$=F.length>0,k={padding:D,boundary:F.filter(dc),altBoundary:$},{refs:j,floatingStyles:G,placement:N,isPositioned:B,middlewareData:_}=qi({strategy:"fixed",placement:O,whileElementsMounted:(...ke)=>Gi(...ke,{animationFrame:g==="always"}),elements:{reference:w.anchor},middleware:[Qi({mainAxis:o+P,alignmentAxis:i}),u&&Ji({mainAxis:!0,crossAxis:!1,limiter:p==="partial"?ec():void 0,...k}),u&&tc({...k}),nc({...k,apply:({elements:ke,rects:Rn,availableWidth:aa,availableHeight:sa})=>{const{width:ia,height:ca}=Rn.reference,Ye=ke.floating.style;Ye.setProperty("--radix-popper-available-width",`${aa}px`),Ye.setProperty("--radix-popper-available-height",`${sa}px`),Ye.setProperty("--radix-popper-anchor-width",`${ia}px`),Ye.setProperty("--radix-popper-anchor-height",`${ca}px`)}}),C&&oc({element:C,padding:c}),fc({arrowWidth:R,arrowHeight:P}),h&&rc({strategy:"referenceHidden",...k})]}),[S,L]=Ur(N),H=te(v);Ae(()=>{B&&(H==null||H())},[B,H]);const Q=(oe=_.arrow)==null?void 0:oe.x,Ne=(je=_.arrow)==null?void 0:je.y,Oe=((z=_.arrow)==null?void 0:z.centerOffset)!==0,[ze,me]=l.useState();return Ae(()=>{x&&me(window.getComputedStyle(x).zIndex)},[x]),s.jsx("div",{ref:j.setFloating,"data-radix-popper-content-wrapper":"",style:{...G,transform:B?G.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ze,"--radix-popper-transform-origin":[(Ie=_.transformOrigin)==null?void 0:Ie.x,(Cn=_.transformOrigin)==null?void 0:Cn.y].join(" "),...((En=_.hide)==null?void 0:En.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:s.jsx(cc,{scope:n,placedSide:S,onArrowChange:E,arrowX:Q,arrowY:Ne,shouldHideArrow:Oe,children:s.jsx(I.div,{"data-side":S,"data-align":L,...m,ref:y,style:{...m.style,animation:B?void 0:"none"}})})})});Gr.displayName=un;var Hr="PopperArrow",uc={top:"bottom",right:"left",bottom:"top",left:"right"},Kr=l.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,a=lc(Hr,r),i=uc[a.placedSide];return s.jsx("span",{ref:a.onArrowChange,style:{position:"absolute",left:a.arrowX,top:a.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[a.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[a.placedSide],visibility:a.shouldHideArrow?"hidden":void 0},children:s.jsx(sc,{...o,ref:n,style:{...o.style,display:"block"}})})});Kr.displayName=Hr;function dc(e){return e!==null}var fc=e=>({name:"transformOrigin",options:e,fn(t){var w,x,b;const{placement:n,rects:r,middlewareData:o}=t,i=((w=o.arrow)==null?void 0:w.centerOffset)!==0,c=i?0:e.arrowWidth,u=i?0:e.arrowHeight,[d,f]=Ur(n),p={start:"0%",center:"50%",end:"100%"}[f],h=(((x=o.arrow)==null?void 0:x.x)??0)+c/2,g=(((b=o.arrow)==null?void 0:b.y)??0)+u/2;let v="",m="";return d==="bottom"?(v=i?p:`${h}px`,m=`${-u}px`):d==="top"?(v=i?p:`${h}px`,m=`${r.floating.height+u}px`):d==="right"?(v=`${-u}px`,m=i?p:`${g}px`):d==="left"&&(v=`${r.floating.width+u}px`,m=i?p:`${g}px`),{data:{x:v,y:m}}}});function Ur(e){const[t,n="center"]=e.split("-");return[t,n]}var Vr=$r,zr=Wr,Yr=Gr,Xr=Kr,pc="VisuallyHidden",qr=l.forwardRef((e,t)=>s.jsx(I.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));qr.displayName=pc;var mc=qr,[xt,wd]=pe("Tooltip",[vt]),wt=vt(),Zr="TooltipProvider",hc=700,Kt="tooltip.open",[gc,dn]=xt(Zr),Qr=e=>{const{__scopeTooltip:t,delayDuration:n=hc,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:a}=e,[i,c]=l.useState(!0),u=l.useRef(!1),d=l.useRef(0);return l.useEffect(()=>{const f=d.current;return()=>window.clearTimeout(f)},[]),s.jsx(gc,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:l.useCallback(()=>{window.clearTimeout(d.current),c(!1)},[]),onClose:l.useCallback(()=>{window.clearTimeout(d.current),d.current=window.setTimeout(()=>c(!0),r)},[r]),isPointerInTransitRef:u,onPointerInTransitChange:l.useCallback(f=>{u.current=f},[]),disableHoverableContent:o,children:a})};Qr.displayName=Zr;var bt="Tooltip",[vc,Ke]=xt(bt),Jr=e=>{const{__scopeTooltip:t,children:n,open:r,defaultOpen:o=!1,onOpenChange:a,disableHoverableContent:i,delayDuration:c}=e,u=dn(bt,e.__scopeTooltip),d=wt(t),[f,p]=l.useState(null),h=ge(),g=l.useRef(0),v=i??u.disableHoverableContent,m=c??u.delayDuration,w=l.useRef(!1),[x=!1,b]=ut({prop:r,defaultProp:o,onChange:R=>{R?(u.onOpen(),document.dispatchEvent(new CustomEvent(Kt))):u.onClose(),a==null||a(R)}}),y=l.useMemo(()=>x?w.current?"delayed-open":"instant-open":"closed",[x]),C=l.useCallback(()=>{window.clearTimeout(g.current),g.current=0,w.current=!1,b(!0)},[b]),E=l.useCallback(()=>{window.clearTimeout(g.current),g.current=0,b(!1)},[b]),M=l.useCallback(()=>{window.clearTimeout(g.current),g.current=window.setTimeout(()=>{w.current=!0,b(!0),g.current=0},m)},[m,b]);return l.useEffect(()=>()=>{g.current&&(window.clearTimeout(g.current),g.current=0)},[]),s.jsx(Vr,{...d,children:s.jsx(vc,{scope:t,contentId:h,open:x,stateAttribute:y,trigger:f,onTriggerChange:p,onTriggerEnter:l.useCallback(()=>{u.isOpenDelayed?M():C()},[u.isOpenDelayed,M,C]),onTriggerLeave:l.useCallback(()=>{v?E():(window.clearTimeout(g.current),g.current=0)},[E,v]),onOpen:C,onClose:E,disableHoverableContent:v,children:n})})};Jr.displayName=bt;var Ut="TooltipTrigger",eo=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Ke(Ut,n),a=dn(Ut,n),i=wt(n),c=l.useRef(null),u=W(t,c,o.onTriggerChange),d=l.useRef(!1),f=l.useRef(!1),p=l.useCallback(()=>d.current=!1,[]);return l.useEffect(()=>()=>document.removeEventListener("pointerup",p),[p]),s.jsx(zr,{asChild:!0,...i,children:s.jsx(I.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:u,onPointerMove:A(e.onPointerMove,h=>{h.pointerType!=="touch"&&!f.current&&!a.isPointerInTransitRef.current&&(o.onTriggerEnter(),f.current=!0)}),onPointerLeave:A(e.onPointerLeave,()=>{o.onTriggerLeave(),f.current=!1}),onPointerDown:A(e.onPointerDown,()=>{d.current=!0,document.addEventListener("pointerup",p,{once:!0})}),onFocus:A(e.onFocus,()=>{d.current||o.onOpen()}),onBlur:A(e.onBlur,o.onClose),onClick:A(e.onClick,o.onClose)})})});eo.displayName=Ut;var fn="TooltipPortal",[xc,wc]=xt(fn,{forceMount:void 0}),to=e=>{const{__scopeTooltip:t,forceMount:n,children:r,container:o}=e,a=Ke(fn,t);return s.jsx(xc,{scope:t,forceMount:n,children:s.jsx(ce,{present:n||a.open,children:s.jsx(ft,{asChild:!0,container:o,children:r})})})};to.displayName=fn;var _e="TooltipContent",no=l.forwardRef((e,t)=>{const n=wc(_e,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...a}=e,i=Ke(_e,e.__scopeTooltip);return s.jsx(ce,{present:r||i.open,children:i.disableHoverableContent?s.jsx(ro,{side:o,...a,ref:t}):s.jsx(bc,{side:o,...a,ref:t})})}),bc=l.forwardRef((e,t)=>{const n=Ke(_e,e.__scopeTooltip),r=dn(_e,e.__scopeTooltip),o=l.useRef(null),a=W(t,o),[i,c]=l.useState(null),{trigger:u,onClose:d}=n,f=o.current,{onPointerInTransitChange:p}=r,h=l.useCallback(()=>{c(null),p(!1)},[p]),g=l.useCallback((v,m)=>{const w=v.currentTarget,x={x:v.clientX,y:v.clientY},b=Ec(x,w.getBoundingClientRect()),y=Rc(x,b),C=Sc(m.getBoundingClientRect()),E=Ac([...y,...C]);c(E),p(!0)},[p]);return l.useEffect(()=>()=>h(),[h]),l.useEffect(()=>{if(u&&f){const v=w=>g(w,f),m=w=>g(w,u);return u.addEventListener("pointerleave",v),f.addEventListener("pointerleave",m),()=>{u.removeEventListener("pointerleave",v),f.removeEventListener("pointerleave",m)}}},[u,f,g,h]),l.useEffect(()=>{if(i){const v=m=>{const w=m.target,x={x:m.clientX,y:m.clientY},b=(u==null?void 0:u.contains(w))||(f==null?void 0:f.contains(w)),y=!Mc(x,i);b?h():y&&(h(),d())};return document.addEventListener("pointermove",v),()=>document.removeEventListener("pointermove",v)}},[u,f,i,d,h]),s.jsx(ro,{...e,ref:a})}),[yc,Cc]=xt(bt,{isInside:!1}),ro=l.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:a,onPointerDownOutside:i,...c}=e,u=Ke(_e,n),d=wt(n),{onClose:f}=u;return l.useEffect(()=>(document.addEventListener(Kt,f),()=>document.removeEventListener(Kt,f)),[f]),l.useEffect(()=>{if(u.trigger){const p=h=>{const g=h.target;g!=null&&g.contains(u.trigger)&&f()};return window.addEventListener("scroll",p,{capture:!0}),()=>window.removeEventListener("scroll",p,{capture:!0})}},[u.trigger,f]),s.jsx(dt,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:a,onPointerDownOutside:i,onFocusOutside:p=>p.preventDefault(),onDismiss:f,children:s.jsxs(Yr,{"data-state":u.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[s.jsx(da,{children:r}),s.jsx(yc,{scope:n,isInside:!0,children:s.jsx(mc,{id:u.contentId,role:"tooltip",children:o||r})})]})})});no.displayName=_e;var oo="TooltipArrow",ao=l.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=wt(n);return Cc(oo,n).isInside?null:s.jsx(Xr,{...o,...r,ref:t})});ao.displayName=oo;function Ec(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(n,r,o,a)){case a:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Rc(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Sc(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function Mc(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a].x,u=t[a].y,d=t[i].x,f=t[i].y;u>r!=f>r&&n<(d-c)*(r-u)/(f-u)+c&&(o=!o)}return o}function Ac(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Pc(t)}function Pc(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const a=t[t.length-1],i=t[t.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const a=n[n.length-1],i=n[n.length-2];if((a.x-i.x)*(o.y-i.y)>=(a.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var _c=Qr,Tc=Jr,Dc=eo,Nc=to,Oc=no,jc=ao;function so({delayDuration:e=0,...t}){return s.jsx(_c,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function Ic({...e}){return s.jsx(so,{children:s.jsx(Tc,{"data-slot":"tooltip",...e})})}function kc({...e}){return s.jsx(Dc,{"data-slot":"tooltip-trigger",...e})}function Lc({className:e,sideOffset:t=4,children:n,...r}){return s.jsx(Nc,{children:s.jsxs(Oc,{"data-slot":"tooltip-content",sideOffset:t,className:T("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-w-sm rounded-md px-3 py-1.5 text-xs",e),...r,children:[n,s.jsx(jc,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}const Fc="sidebar_state",$c=60*60*24*7,Bc="16rem",Wc="18rem",Gc="3rem",Hc="b",io=l.createContext(null);function yt(){const e=l.useContext(io);if(!e)throw new Error("useSidebar must be used within a SidebarProvider.");return e}function Kc({defaultOpen:e=!0,open:t,onOpenChange:n,className:r,style:o,children:a,...i}){const c=qn(),[u,d]=l.useState(!1),[f,p]=l.useState(e),h=t??f,g=l.useCallback(x=>{const b=typeof x=="function"?x(h):x;n?n(b):p(b),document.cookie=`${Fc}=${b}; path=/; max-age=${$c}`},[n,h]),v=l.useCallback(()=>c?d(x=>!x):g(x=>!x),[c,g,d]);l.useEffect(()=>{const x=b=>{b.key===Hc&&(b.metaKey||b.ctrlKey)&&(b.preventDefault(),v())};return window.addEventListener("keydown",x),()=>window.removeEventListener("keydown",x)},[v]);const m=h?"expanded":"collapsed",w=l.useMemo(()=>({state:m,open:h,setOpen:g,isMobile:c,openMobile:u,setOpenMobile:d,toggleSidebar:v}),[m,h,g,c,u,d,v]);return s.jsx(io.Provider,{value:w,children:s.jsx(so,{delayDuration:0,children:s.jsx("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":Bc,"--sidebar-width-icon":Gc,...o},className:T("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...i,children:a})})})}function Uc({side:e="left",variant:t="sidebar",collapsible:n="offcanvas",className:r,children:o,...a}){const{isMobile:i,state:c,openMobile:u,setOpenMobile:d}=yt();return n==="none"?s.jsx("div",{"data-slot":"sidebar",className:T("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...a,children:o}):i?s.jsxs(ti,{open:u,onOpenChange:d,...a,children:[s.jsxs(ai,{className:"sr-only",children:[s.jsx(si,{children:"Sidebar"}),s.jsx(ii,{children:"Displays the mobile sidebar."})]}),s.jsx(oi,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":Wc},side:e,children:s.jsx("div",{className:"flex h-full w-full flex-col",children:o})})]}):s.jsxs("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":c==="collapsed"?n:"","data-variant":t,"data-side":e,"data-slot":"sidebar",children:[s.jsx("div",{className:T("relative h-svh w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180",t==="floating"||t==="inset"?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),s.jsx("div",{className:T("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex",e==="left"?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]",t==="floating"||t==="inset"?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...a,children:s.jsx("div",{"data-sidebar":"sidebar",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:o})})]})}function Vc({className:e,onClick:t,...n}){const{toggleSidebar:r}=yt();return s.jsxs(pa,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:T("h-7 w-7",e),onClick:o=>{t==null||t(o),r()},...n,children:[s.jsx(Na,{}),s.jsx("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function zc({className:e,...t}){return s.jsx("main",{"data-slot":"sidebar-inset",className:T("bg-background relative flex max-w-full min-h-svh flex-1 flex-col","peer-data-[variant=inset]:min-h-[calc(100svh-(--spacing(4)))] md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-0",e),...t})}function Yc({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:T("flex flex-col gap-2 p-2",e),...t})}function Xc({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:T("flex flex-col gap-2 p-2",e),...t})}function qc({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:T("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",e),...t})}function co({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:T("relative flex w-full min-w-0 flex-col p-2",e),...t})}function Zc({className:e,asChild:t=!1,...n}){const r=t?ve:"div";return s.jsx(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:T("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",e),...n})}function Qc({className:e,...t}){return s.jsx("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:T("w-full text-sm",e),...t})}function Ct({className:e,...t}){return s.jsx("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:T("flex w-full min-w-0 flex-col gap-1",e),...t})}function Et({className:e,...t}){return s.jsx("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:T("group/menu-item relative",e),...t})}const Jc=fa("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function Rt({asChild:e=!1,isActive:t=!1,variant:n="default",size:r="default",tooltip:o,className:a,...i}){const c=e?ve:"button",{isMobile:u,state:d}=yt(),f=s.jsx(c,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":t,className:T(Jc({variant:n,size:r}),a),...i});return o?(typeof o=="string"&&(o={children:o}),s.jsxs(Ic,{children:[s.jsx(kc,{asChild:!0,children:f}),s.jsx(Lc,{side:"right",align:"center",hidden:d!=="collapsed"||u,...o})]})):f}function el({variant:e="header",children:t,...n}){return e==="sidebar"?s.jsx(zc,{...n,children:t}):s.jsx("main",{className:"mx-auto flex h-full w-full max-w-7xl flex-1 flex-col gap-4 rounded-xl",...n,children:t})}function tl({children:e,variant:t="header"}){const n=Yt().props.sidebarOpen;return t==="header"?s.jsx("div",{className:"flex min-h-screen w-full flex-col",children:e}):s.jsx(Kc,{defaultOpen:n,children:e})}function nl({iconNode:e,className:t,...n}){return s.jsx(e,{className:T("h-4 w-4",t),...n})}function rl({items:e,className:t,...n}){return s.jsx(co,{...n,className:`group-data-[collapsible=icon]:p-0 ${t||""}`,children:s.jsx(Qc,{children:s.jsx(Ct,{children:e.map(r=>s.jsx(Et,{children:s.jsx(Rt,{asChild:!0,className:"text-neutral-600 hover:text-neutral-800 dark:text-neutral-300 dark:hover:text-neutral-100",children:s.jsxs("a",{href:r.href,target:"_blank",rel:"noopener noreferrer",children:[r.icon&&s.jsx(nl,{iconNode:r.icon,className:"h-5 w-5"}),s.jsx("span",{children:r.title})]})})},r.title))})})})}function ol({items:e=[]}){const t=Yt();return s.jsxs(co,{className:"px-2 py-0",children:[s.jsx(Zc,{children:"Platform"}),s.jsx(Ct,{children:e.map(n=>s.jsx(Et,{children:s.jsx(Rt,{asChild:!0,isActive:n.href===t.url,tooltip:{children:n.title},children:s.jsxs(Fe,{href:n.href,prefetch:!0,children:[n.icon&&s.jsx(n.icon,{}),s.jsx("span",{children:n.title})]})})},n.title))})]})}function lo(e){const t=e+"CollectionProvider",[n,r]=pe(t),[o,a]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=g=>{const{scope:v,children:m}=g,w=he.useRef(null),x=he.useRef(new Map).current;return s.jsx(o,{scope:v,itemMap:x,collectionRef:w,children:m})};i.displayName=t;const c=e+"CollectionSlot",u=he.forwardRef((g,v)=>{const{scope:m,children:w}=g,x=a(c,m),b=W(v,x.collectionRef);return s.jsx(ve,{ref:b,children:w})});u.displayName=c;const d=e+"CollectionItemSlot",f="data-radix-collection-item",p=he.forwardRef((g,v)=>{const{scope:m,children:w,...x}=g,b=he.useRef(null),y=W(v,b),C=a(d,m);return he.useEffect(()=>(C.itemMap.set(b,{ref:b,...x}),()=>void C.itemMap.delete(b))),s.jsx(ve,{[f]:"",ref:y,children:w})});p.displayName=d;function h(g){const v=a(e+"CollectionConsumer",g);return he.useCallback(()=>{const w=v.collectionRef.current;if(!w)return[];const x=Array.from(w.querySelectorAll(`[${f}]`));return Array.from(v.itemMap.values()).sort((C,E)=>x.indexOf(C.ref.current)-x.indexOf(E.ref.current))},[v.collectionRef,v.itemMap])}return[{Provider:i,Slot:u,ItemSlot:p},h,r]}var al=l.createContext(void 0);function uo(e){const t=l.useContext(al);return e||t||"ltr"}var Ft="rovingFocusGroup.onEntryFocus",sl={bubbles:!1,cancelable:!0},St="RovingFocusGroup",[Vt,fo,il]=lo(St),[cl,po]=pe(St,[il]),[ll,ul]=cl(St),mo=l.forwardRef((e,t)=>s.jsx(Vt.Provider,{scope:e.__scopeRovingFocusGroup,children:s.jsx(Vt.Slot,{scope:e.__scopeRovingFocusGroup,children:s.jsx(dl,{...e,ref:t})})}));mo.displayName=St;var dl=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,orientation:r,loop:o=!1,dir:a,currentTabStopId:i,defaultCurrentTabStopId:c,onCurrentTabStopIdChange:u,onEntryFocus:d,preventScrollOnEntryFocus:f=!1,...p}=e,h=l.useRef(null),g=W(t,h),v=uo(a),[m=null,w]=ut({prop:i,defaultProp:c,onChange:u}),[x,b]=l.useState(!1),y=te(d),C=fo(n),E=l.useRef(!1),[M,R]=l.useState(0);return l.useEffect(()=>{const P=h.current;if(P)return P.addEventListener(Ft,y),()=>P.removeEventListener(Ft,y)},[y]),s.jsx(ll,{scope:n,orientation:r,dir:v,loop:o,currentTabStopId:m,onItemFocus:l.useCallback(P=>w(P),[w]),onItemShiftTab:l.useCallback(()=>b(!0),[]),onFocusableItemAdd:l.useCallback(()=>R(P=>P+1),[]),onFocusableItemRemove:l.useCallback(()=>R(P=>P-1),[]),children:s.jsx(I.div,{tabIndex:x||M===0?-1:0,"data-orientation":r,...p,ref:g,style:{outline:"none",...e.style},onMouseDown:A(e.onMouseDown,()=>{E.current=!0}),onFocus:A(e.onFocus,P=>{const O=!E.current;if(P.target===P.currentTarget&&O&&!x){const D=new CustomEvent(Ft,sl);if(P.currentTarget.dispatchEvent(D),!D.defaultPrevented){const F=C().filter(N=>N.focusable),$=F.find(N=>N.active),k=F.find(N=>N.id===m),G=[$,k,...F].filter(Boolean).map(N=>N.ref.current);vo(G,f)}}E.current=!1}),onBlur:A(e.onBlur,()=>b(!1))})})}),ho="RovingFocusGroupItem",go=l.forwardRef((e,t)=>{const{__scopeRovingFocusGroup:n,focusable:r=!0,active:o=!1,tabStopId:a,...i}=e,c=ge(),u=a||c,d=ul(ho,n),f=d.currentTabStopId===u,p=fo(n),{onFocusableItemAdd:h,onFocusableItemRemove:g}=d;return l.useEffect(()=>{if(r)return h(),()=>g()},[r,h,g]),s.jsx(Vt.ItemSlot,{scope:n,id:u,focusable:r,active:o,children:s.jsx(I.span,{tabIndex:f?0:-1,"data-orientation":d.orientation,...i,ref:t,onMouseDown:A(e.onMouseDown,v=>{r?d.onItemFocus(u):v.preventDefault()}),onFocus:A(e.onFocus,()=>d.onItemFocus(u)),onKeyDown:A(e.onKeyDown,v=>{if(v.key==="Tab"&&v.shiftKey){d.onItemShiftTab();return}if(v.target!==v.currentTarget)return;const m=ml(v,d.orientation,d.dir);if(m!==void 0){if(v.metaKey||v.ctrlKey||v.altKey||v.shiftKey)return;v.preventDefault();let x=p().filter(b=>b.focusable).map(b=>b.ref.current);if(m==="last")x.reverse();else if(m==="prev"||m==="next"){m==="prev"&&x.reverse();const b=x.indexOf(v.currentTarget);x=d.loop?hl(x,b+1):x.slice(b+1)}setTimeout(()=>vo(x))}})})})});go.displayName=ho;var fl={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function pl(e,t){return t!=="rtl"?e:e==="ArrowLeft"?"ArrowRight":e==="ArrowRight"?"ArrowLeft":e}function ml(e,t,n){const r=pl(e.key,n);if(!(t==="vertical"&&["ArrowLeft","ArrowRight"].includes(r))&&!(t==="horizontal"&&["ArrowUp","ArrowDown"].includes(r)))return fl[r]}function vo(e,t=!1){const n=document.activeElement;for(const r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}function hl(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var gl=mo,vl=go,zt=["Enter"," "],xl=["ArrowDown","PageUp","Home"],xo=["ArrowUp","PageDown","End"],wl=[...xl,...xo],bl={ltr:[...zt,"ArrowRight"],rtl:[...zt,"ArrowLeft"]},yl={ltr:["ArrowLeft"],rtl:["ArrowRight"]},Ue="Menu",[We,Cl,El]=lo(Ue),[be,wo]=pe(Ue,[El,vt,po]),Mt=vt(),bo=po(),[Rl,ye]=be(Ue),[Sl,Ve]=be(Ue),yo=e=>{const{__scopeMenu:t,open:n=!1,children:r,dir:o,onOpenChange:a,modal:i=!0}=e,c=Mt(t),[u,d]=l.useState(null),f=l.useRef(!1),p=te(a),h=uo(o);return l.useEffect(()=>{const g=()=>{f.current=!0,document.addEventListener("pointerdown",v,{capture:!0,once:!0}),document.addEventListener("pointermove",v,{capture:!0,once:!0})},v=()=>f.current=!1;return document.addEventListener("keydown",g,{capture:!0}),()=>{document.removeEventListener("keydown",g,{capture:!0}),document.removeEventListener("pointerdown",v,{capture:!0}),document.removeEventListener("pointermove",v,{capture:!0})}},[]),s.jsx(Vr,{...c,children:s.jsx(Rl,{scope:t,open:n,onOpenChange:p,content:u,onContentChange:d,children:s.jsx(Sl,{scope:t,onClose:l.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:h,modal:i,children:r})})})};yo.displayName=Ue;var Ml="MenuAnchor",pn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Mt(n);return s.jsx(zr,{...o,...r,ref:t})});pn.displayName=Ml;var mn="MenuPortal",[Al,Co]=be(mn,{forceMount:void 0}),Eo=e=>{const{__scopeMenu:t,forceMount:n,children:r,container:o}=e,a=ye(mn,t);return s.jsx(Al,{scope:t,forceMount:n,children:s.jsx(ce,{present:n||a.open,children:s.jsx(ft,{asChild:!0,container:o,children:r})})})};Eo.displayName=mn;var Y="MenuContent",[Pl,hn]=be(Y),Ro=l.forwardRef((e,t)=>{const n=Co(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=ye(Y,e.__scopeMenu),i=Ve(Y,e.__scopeMenu);return s.jsx(We.Provider,{scope:e.__scopeMenu,children:s.jsx(ce,{present:r||a.open,children:s.jsx(We.Slot,{scope:e.__scopeMenu,children:i.modal?s.jsx(_l,{...o,ref:t}):s.jsx(Tl,{...o,ref:t})})})})}),_l=l.forwardRef((e,t)=>{const n=ye(Y,e.__scopeMenu),r=l.useRef(null),o=W(t,r);return l.useEffect(()=>{const a=r.current;if(a)return lr(a)},[]),s.jsx(gn,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:A(e.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Tl=l.forwardRef((e,t)=>{const n=ye(Y,e.__scopeMenu);return s.jsx(gn,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),gn=l.forwardRef((e,t)=>{const{__scopeMenu:n,loop:r=!1,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,disableOutsidePointerEvents:c,onEntryFocus:u,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:h,onDismiss:g,disableOutsideScroll:v,...m}=e,w=ye(Y,n),x=Ve(Y,n),b=Mt(n),y=bo(n),C=Cl(n),[E,M]=l.useState(null),R=l.useRef(null),P=W(t,R,w.onContentChange),O=l.useRef(0),D=l.useRef(""),F=l.useRef(0),$=l.useRef(null),k=l.useRef("right"),j=l.useRef(0),G=v?qt:l.Fragment,N=v?{as:ve,allowPinchZoom:!0}:void 0,B=S=>{var oe,je;const L=D.current+S,H=C().filter(z=>!z.disabled),Q=document.activeElement,Ne=(oe=H.find(z=>z.ref.current===Q))==null?void 0:oe.textValue,Oe=H.map(z=>z.textValue),ze=Gl(Oe,L,Ne),me=(je=H.find(z=>z.textValue===ze))==null?void 0:je.ref.current;(function z(Ie){D.current=Ie,window.clearTimeout(O.current),Ie!==""&&(O.current=window.setTimeout(()=>z(""),1e3))})(L),me&&setTimeout(()=>me.focus())};l.useEffect(()=>()=>window.clearTimeout(O.current),[]),er();const _=l.useCallback(S=>{var H,Q;return k.current===((H=$.current)==null?void 0:H.side)&&Kl(S,(Q=$.current)==null?void 0:Q.area)},[]);return s.jsx(Pl,{scope:n,searchRef:D,onItemEnter:l.useCallback(S=>{_(S)&&S.preventDefault()},[_]),onItemLeave:l.useCallback(S=>{var L;_(S)||((L=R.current)==null||L.focus(),M(null))},[_]),onTriggerLeave:l.useCallback(S=>{_(S)&&S.preventDefault()},[_]),pointerGraceTimerRef:F,onPointerGraceIntentChange:l.useCallback(S=>{$.current=S},[]),children:s.jsx(G,{...N,children:s.jsx(Xt,{asChild:!0,trapped:o,onMountAutoFocus:A(a,S=>{var L;S.preventDefault(),(L=R.current)==null||L.focus({preventScroll:!0})}),onUnmountAutoFocus:i,children:s.jsx(dt,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:h,onDismiss:g,children:s.jsx(gl,{asChild:!0,...y,dir:x.dir,orientation:"vertical",loop:r,currentTabStopId:E,onCurrentTabStopIdChange:M,onEntryFocus:A(u,S=>{x.isUsingKeyboardRef.current||S.preventDefault()}),preventScrollOnEntryFocus:!0,children:s.jsx(Yr,{role:"menu","aria-orientation":"vertical","data-state":Bo(w.open),"data-radix-menu-content":"",dir:x.dir,...b,...m,ref:P,style:{outline:"none",...m.style},onKeyDown:A(m.onKeyDown,S=>{const H=S.target.closest("[data-radix-menu-content]")===S.currentTarget,Q=S.ctrlKey||S.altKey||S.metaKey,Ne=S.key.length===1;H&&(S.key==="Tab"&&S.preventDefault(),!Q&&Ne&&B(S.key));const Oe=R.current;if(S.target!==Oe||!wl.includes(S.key))return;S.preventDefault();const me=C().filter(oe=>!oe.disabled).map(oe=>oe.ref.current);xo.includes(S.key)&&me.reverse(),Bl(me)}),onBlur:A(e.onBlur,S=>{S.currentTarget.contains(S.target)||(window.clearTimeout(O.current),D.current="")}),onPointerMove:A(e.onPointerMove,Ge(S=>{const L=S.target,H=j.current!==S.clientX;if(S.currentTarget.contains(L)&&H){const Q=S.clientX>j.current?"right":"left";k.current=Q,j.current=S.clientX}}))})})})})})})});Ro.displayName=Y;var Dl="MenuGroup",vn=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"group",...r,ref:t})});vn.displayName=Dl;var Nl="MenuLabel",So=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{...r,ref:t})});So.displayName=Nl;var ct="MenuItem",Un="menu.itemSelect",At=l.forwardRef((e,t)=>{const{disabled:n=!1,onSelect:r,...o}=e,a=l.useRef(null),i=Ve(ct,e.__scopeMenu),c=hn(ct,e.__scopeMenu),u=W(t,a),d=l.useRef(!1),f=()=>{const p=a.current;if(!n&&p){const h=new CustomEvent(Un,{bubbles:!0,cancelable:!0});p.addEventListener(Un,g=>r==null?void 0:r(g),{once:!0}),Xn(p,h),h.defaultPrevented?d.current=!1:i.onClose()}};return s.jsx(Mo,{...o,ref:u,disabled:n,onClick:A(e.onClick,f),onPointerDown:p=>{var h;(h=e.onPointerDown)==null||h.call(e,p),d.current=!0},onPointerUp:A(e.onPointerUp,p=>{var h;d.current||(h=p.currentTarget)==null||h.click()}),onKeyDown:A(e.onKeyDown,p=>{const h=c.searchRef.current!=="";n||h&&p.key===" "||zt.includes(p.key)&&(p.currentTarget.click(),p.preventDefault())})})});At.displayName=ct;var Mo=l.forwardRef((e,t)=>{const{__scopeMenu:n,disabled:r=!1,textValue:o,...a}=e,i=hn(ct,n),c=bo(n),u=l.useRef(null),d=W(t,u),[f,p]=l.useState(!1),[h,g]=l.useState("");return l.useEffect(()=>{const v=u.current;v&&g((v.textContent??"").trim())},[a.children]),s.jsx(We.ItemSlot,{scope:n,disabled:r,textValue:o??h,children:s.jsx(vl,{asChild:!0,...c,focusable:!r,children:s.jsx(I.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...a,ref:d,onPointerMove:A(e.onPointerMove,Ge(v=>{r?i.onItemLeave(v):(i.onItemEnter(v),v.defaultPrevented||v.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:A(e.onPointerLeave,Ge(v=>i.onItemLeave(v))),onFocus:A(e.onFocus,()=>p(!0)),onBlur:A(e.onBlur,()=>p(!1))})})})}),Ol="MenuCheckboxItem",Ao=l.forwardRef((e,t)=>{const{checked:n=!1,onCheckedChange:r,...o}=e;return s.jsx(No,{scope:e.__scopeMenu,checked:n,children:s.jsx(At,{role:"menuitemcheckbox","aria-checked":lt(n)?"mixed":n,...o,ref:t,"data-state":wn(n),onSelect:A(o.onSelect,()=>r==null?void 0:r(lt(n)?!0:!n),{checkForDefaultPrevented:!1})})})});Ao.displayName=Ol;var Po="MenuRadioGroup",[jl,Il]=be(Po,{value:void 0,onValueChange:()=>{}}),_o=l.forwardRef((e,t)=>{const{value:n,onValueChange:r,...o}=e,a=te(r);return s.jsx(jl,{scope:e.__scopeMenu,value:n,onValueChange:a,children:s.jsx(vn,{...o,ref:t})})});_o.displayName=Po;var To="MenuRadioItem",Do=l.forwardRef((e,t)=>{const{value:n,...r}=e,o=Il(To,e.__scopeMenu),a=n===o.value;return s.jsx(No,{scope:e.__scopeMenu,checked:a,children:s.jsx(At,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":wn(a),onSelect:A(r.onSelect,()=>{var i;return(i=o.onValueChange)==null?void 0:i.call(o,n)},{checkForDefaultPrevented:!1})})})});Do.displayName=To;var xn="MenuItemIndicator",[No,kl]=be(xn,{checked:!1}),Oo=l.forwardRef((e,t)=>{const{__scopeMenu:n,forceMount:r,...o}=e,a=kl(xn,n);return s.jsx(ce,{present:r||lt(a.checked)||a.checked===!0,children:s.jsx(I.span,{...o,ref:t,"data-state":wn(a.checked)})})});Oo.displayName=xn;var Ll="MenuSeparator",jo=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e;return s.jsx(I.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});jo.displayName=Ll;var Fl="MenuArrow",Io=l.forwardRef((e,t)=>{const{__scopeMenu:n,...r}=e,o=Mt(n);return s.jsx(Xr,{...o,...r,ref:t})});Io.displayName=Fl;var $l="MenuSub",[bd,ko]=be($l),Le="MenuSubTrigger",Lo=l.forwardRef((e,t)=>{const n=ye(Le,e.__scopeMenu),r=Ve(Le,e.__scopeMenu),o=ko(Le,e.__scopeMenu),a=hn(Le,e.__scopeMenu),i=l.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:u}=a,d={__scopeMenu:e.__scopeMenu},f=l.useCallback(()=>{i.current&&window.clearTimeout(i.current),i.current=null},[]);return l.useEffect(()=>f,[f]),l.useEffect(()=>{const p=c.current;return()=>{window.clearTimeout(p),u(null)}},[c,u]),s.jsx(pn,{asChild:!0,...d,children:s.jsx(Mo,{id:o.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":o.contentId,"data-state":Bo(n.open),...e,ref:Yn(t,o.onTriggerChange),onClick:p=>{var h;(h=e.onClick)==null||h.call(e,p),!(e.disabled||p.defaultPrevented)&&(p.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:A(e.onPointerMove,Ge(p=>{a.onItemEnter(p),!p.defaultPrevented&&!e.disabled&&!n.open&&!i.current&&(a.onPointerGraceIntentChange(null),i.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100))})),onPointerLeave:A(e.onPointerLeave,Ge(p=>{var g,v;f();const h=(g=n.content)==null?void 0:g.getBoundingClientRect();if(h){const m=(v=n.content)==null?void 0:v.dataset.side,w=m==="right",x=w?-5:5,b=h[w?"left":"right"],y=h[w?"right":"left"];a.onPointerGraceIntentChange({area:[{x:p.clientX+x,y:p.clientY},{x:b,y:h.top},{x:y,y:h.top},{x:y,y:h.bottom},{x:b,y:h.bottom}],side:m}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>a.onPointerGraceIntentChange(null),300)}else{if(a.onTriggerLeave(p),p.defaultPrevented)return;a.onPointerGraceIntentChange(null)}})),onKeyDown:A(e.onKeyDown,p=>{var g;const h=a.searchRef.current!=="";e.disabled||h&&p.key===" "||bl[r.dir].includes(p.key)&&(n.onOpenChange(!0),(g=n.content)==null||g.focus(),p.preventDefault())})})})});Lo.displayName=Le;var Fo="MenuSubContent",$o=l.forwardRef((e,t)=>{const n=Co(Y,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,a=ye(Y,e.__scopeMenu),i=Ve(Y,e.__scopeMenu),c=ko(Fo,e.__scopeMenu),u=l.useRef(null),d=W(t,u);return s.jsx(We.Provider,{scope:e.__scopeMenu,children:s.jsx(ce,{present:r||a.open,children:s.jsx(We.Slot,{scope:e.__scopeMenu,children:s.jsx(gn,{id:c.contentId,"aria-labelledby":c.triggerId,...o,ref:d,align:"start",side:i.dir==="rtl"?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:f=>{var p;i.isUsingKeyboardRef.current&&((p=u.current)==null||p.focus()),f.preventDefault()},onCloseAutoFocus:f=>f.preventDefault(),onFocusOutside:A(e.onFocusOutside,f=>{f.target!==c.trigger&&a.onOpenChange(!1)}),onEscapeKeyDown:A(e.onEscapeKeyDown,f=>{i.onClose(),f.preventDefault()}),onKeyDown:A(e.onKeyDown,f=>{var g;const p=f.currentTarget.contains(f.target),h=yl[i.dir].includes(f.key);p&&h&&(a.onOpenChange(!1),(g=c.trigger)==null||g.focus(),f.preventDefault())})})})})})});$o.displayName=Fo;function Bo(e){return e?"open":"closed"}function lt(e){return e==="indeterminate"}function wn(e){return lt(e)?"indeterminate":e?"checked":"unchecked"}function Bl(e){const t=document.activeElement;for(const n of e)if(n===t||(n.focus(),document.activeElement!==t))return}function Wl(e,t){return e.map((n,r)=>e[(t+r)%e.length])}function Gl(e,t,n){const o=t.length>1&&Array.from(t).every(d=>d===t[0])?t[0]:t,a=n?e.indexOf(n):-1;let i=Wl(e,Math.max(a,0));o.length===1&&(i=i.filter(d=>d!==n));const u=i.find(d=>d.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}function Hl(e,t){const{x:n,y:r}=e;let o=!1;for(let a=0,i=t.length-1;a<t.length;i=a++){const c=t[a].x,u=t[a].y,d=t[i].x,f=t[i].y;u>r!=f>r&&n<(d-c)*(r-u)/(f-u)+c&&(o=!o)}return o}function Kl(e,t){if(!t)return!1;const n={x:e.clientX,y:e.clientY};return Hl(n,t)}function Ge(e){return t=>t.pointerType==="mouse"?e(t):void 0}var Ul=yo,Vl=pn,zl=Eo,Yl=Ro,Xl=vn,ql=So,Zl=At,Ql=Ao,Jl=_o,eu=Do,tu=Oo,nu=jo,ru=Io,ou=Lo,au=$o,bn="DropdownMenu",[su,yd]=pe(bn,[wo]),K=wo(),[iu,Wo]=su(bn),Go=e=>{const{__scopeDropdownMenu:t,children:n,dir:r,open:o,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,u=K(t),d=l.useRef(null),[f=!1,p]=ut({prop:o,defaultProp:a,onChange:i});return s.jsx(iu,{scope:t,triggerId:ge(),triggerRef:d,contentId:ge(),open:f,onOpenChange:p,onOpenToggle:l.useCallback(()=>p(h=>!h),[p]),modal:c,children:s.jsx(Ul,{...u,open:f,onOpenChange:p,dir:r,modal:c,children:n})})};Go.displayName=bn;var Ho="DropdownMenuTrigger",Ko=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,a=Wo(Ho,n),i=K(n);return s.jsx(Vl,{asChild:!0,...i,children:s.jsx(I.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:Yn(t,a.triggerRef),onPointerDown:A(e.onPointerDown,c=>{!r&&c.button===0&&c.ctrlKey===!1&&(a.onOpenToggle(),a.open||c.preventDefault())}),onKeyDown:A(e.onKeyDown,c=>{r||(["Enter"," "].includes(c.key)&&a.onOpenToggle(),c.key==="ArrowDown"&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(c.key)&&c.preventDefault())})})})});Ko.displayName=Ho;var cu="DropdownMenuPortal",Uo=e=>{const{__scopeDropdownMenu:t,...n}=e,r=K(t);return s.jsx(zl,{...r,...n})};Uo.displayName=cu;var Vo="DropdownMenuContent",zo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=Wo(Vo,n),a=K(n),i=l.useRef(!1);return s.jsx(Yl,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...r,ref:t,onCloseAutoFocus:A(e.onCloseAutoFocus,c=>{var u;i.current||(u=o.triggerRef.current)==null||u.focus(),i.current=!1,c.preventDefault()}),onInteractOutside:A(e.onInteractOutside,c=>{const u=c.detail.originalEvent,d=u.button===0&&u.ctrlKey===!0,f=u.button===2||d;(!o.modal||f)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});zo.displayName=Vo;var lu="DropdownMenuGroup",Yo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Xl,{...o,...r,ref:t})});Yo.displayName=lu;var uu="DropdownMenuLabel",Xo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(ql,{...o,...r,ref:t})});Xo.displayName=uu;var du="DropdownMenuItem",qo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Zl,{...o,...r,ref:t})});qo.displayName=du;var fu="DropdownMenuCheckboxItem",pu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Ql,{...o,...r,ref:t})});pu.displayName=fu;var mu="DropdownMenuRadioGroup",hu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(Jl,{...o,...r,ref:t})});hu.displayName=mu;var gu="DropdownMenuRadioItem",vu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(eu,{...o,...r,ref:t})});vu.displayName=gu;var xu="DropdownMenuItemIndicator",wu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(tu,{...o,...r,ref:t})});wu.displayName=xu;var bu="DropdownMenuSeparator",Zo=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(nu,{...o,...r,ref:t})});Zo.displayName=bu;var yu="DropdownMenuArrow",Cu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(ru,{...o,...r,ref:t})});Cu.displayName=yu;var Eu="DropdownMenuSubTrigger",Ru=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(ou,{...o,...r,ref:t})});Ru.displayName=Eu;var Su="DropdownMenuSubContent",Mu=l.forwardRef((e,t)=>{const{__scopeDropdownMenu:n,...r}=e,o=K(n);return s.jsx(au,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});Mu.displayName=Su;var Au=Go,Pu=Ko,_u=Uo,Tu=zo,Du=Yo,Nu=Xo,Ou=qo,ju=Zo;function Iu({...e}){return s.jsx(Au,{"data-slot":"dropdown-menu",...e})}function ku({...e}){return s.jsx(Pu,{"data-slot":"dropdown-menu-trigger",...e})}function Lu({className:e,sideOffset:t=4,...n}){return s.jsx(_u,{children:s.jsx(Tu,{"data-slot":"dropdown-menu-content",sideOffset:t,className:T("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...n})})}function Fu({...e}){return s.jsx(Du,{"data-slot":"dropdown-menu-group",...e})}function Vn({className:e,inset:t,variant:n="default",...r}){return s.jsx(Ou,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":n,className:T("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r})}function $u({className:e,inset:t,...n}){return s.jsx(Nu,{"data-slot":"dropdown-menu-label","data-inset":t,className:T("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...n})}function zn({className:e,...t}){return s.jsx(ju,{"data-slot":"dropdown-menu-separator",className:T("bg-border -mx-1 my-1 h-px",e),...t})}var yn="Avatar",[Bu,Cd]=pe(yn),[Wu,Qo]=Bu(yn),Jo=l.forwardRef((e,t)=>{const{__scopeAvatar:n,...r}=e,[o,a]=l.useState("idle");return s.jsx(Wu,{scope:n,imageLoadingStatus:o,onImageLoadingStatusChange:a,children:s.jsx(I.span,{...r,ref:t})})});Jo.displayName=yn;var ea="AvatarImage",ta=l.forwardRef((e,t)=>{const{__scopeAvatar:n,src:r,onLoadingStatusChange:o=()=>{},...a}=e,i=Qo(ea,n),c=Gu(r,a.referrerPolicy),u=te(d=>{o(d),i.onImageLoadingStatusChange(d)});return Ae(()=>{c!=="idle"&&u(c)},[c,u]),c==="loaded"?s.jsx(I.img,{...a,ref:t,src:r}):null});ta.displayName=ea;var na="AvatarFallback",ra=l.forwardRef((e,t)=>{const{__scopeAvatar:n,delayMs:r,...o}=e,a=Qo(na,n),[i,c]=l.useState(r===void 0);return l.useEffect(()=>{if(r!==void 0){const u=window.setTimeout(()=>c(!0),r);return()=>window.clearTimeout(u)}},[r]),i&&a.imageLoadingStatus!=="loaded"?s.jsx(I.span,{...o,ref:t}):null});ra.displayName=na;function Gu(e,t){const[n,r]=l.useState("idle");return Ae(()=>{if(!e){r("error");return}let o=!0;const a=new window.Image,i=c=>()=>{o&&r(c)};return r("loading"),a.onload=i("loaded"),a.onerror=i("error"),a.src=e,t&&(a.referrerPolicy=t),()=>{o=!1}},[e,t]),n}var Hu=Jo,Ku=ta,Uu=ra;function Vu({className:e,...t}){return s.jsx(Hu,{"data-slot":"avatar",className:T("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function zu({className:e,...t}){return s.jsx(Ku,{"data-slot":"avatar-image",className:T("aspect-square size-full",e),...t})}function Yu({className:e,...t}){return s.jsx(Uu,{"data-slot":"avatar-fallback",className:T("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}function Xu(){return l.useCallback(e=>{const t=e.trim().split(" ");if(t.length===0)return"";if(t.length===1)return t[0].charAt(0).toUpperCase();const n=t[0].charAt(0),r=t[t.length-1].charAt(0);return`${n}${r}`.toUpperCase()},[])}function oa({user:e,showEmail:t=!1}){const n=Xu();return s.jsxs(s.Fragment,{children:[s.jsxs(Vu,{className:"h-8 w-8 overflow-hidden rounded-full",children:[s.jsx(zu,{src:e.avatar,alt:e.name}),s.jsx(Yu,{className:"rounded-lg bg-neutral-200 text-black dark:bg-neutral-700 dark:text-white",children:n(e.name)})]}),s.jsxs("div",{className:"grid flex-1 text-left text-sm leading-tight",children:[s.jsx("span",{className:"truncate font-medium",children:e.name}),t&&s.jsx("span",{className:"text-muted-foreground truncate text-xs",children:e.email})]})]})}function qu(){return l.useCallback(()=>{document.body.style.removeProperty("pointer-events")},[])}function Zu({user:e}){const t=qu(),n=()=>{t(),ua.flushAll()};return s.jsxs(s.Fragment,{children:[s.jsx($u,{className:"p-0 font-normal",children:s.jsx("div",{className:"flex items-center gap-2 px-1 py-1.5 text-left text-sm",children:s.jsx(oa,{user:e,showEmail:!0})})}),s.jsx(zn,{}),s.jsx(Fu,{children:s.jsx(Vn,{asChild:!0,children:s.jsxs(Fe,{className:"block w-full",href:route("profile.edit"),as:"button",prefetch:!0,onClick:t,children:[s.jsx(ja,{className:"mr-2"}),"Settings"]})})}),s.jsx(zn,{}),s.jsx(Vn,{asChild:!0,children:s.jsxs(Fe,{className:"block w-full",method:"post",href:route("logout"),as:"button",onClick:n,children:[s.jsx(Ta,{className:"mr-2"}),"Log out"]})})]})}function Qu(){const{auth:e}=Yt().props,{state:t}=yt(),n=qn();return s.jsx(Ct,{children:s.jsx(Et,{children:s.jsxs(Iu,{children:[s.jsx(ku,{asChild:!0,children:s.jsxs(Rt,{size:"lg",className:"text-sidebar-accent-foreground data-[state=open]:bg-sidebar-accent group",children:[s.jsx(oa,{user:e.user}),s.jsx(Ra,{className:"ml-auto size-4"})]})}),s.jsx(Lu,{className:"w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg",align:"end",side:n?"bottom":t==="collapsed"?"left":"bottom",children:s.jsx(Zu,{user:e.user})})]})})})}function Ju(){return s.jsxs(s.Fragment,{children:[s.jsx("div",{className:"bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-md",children:s.jsx(ma,{className:"size-5 fill-current text-white dark:text-black"})}),s.jsx("div",{className:"ml-1 grid flex-1 text-left text-sm",children:s.jsx("span",{className:"mb-0.5 truncate leading-none font-semibold",children:"Laravel Starter Kit"})})]})}const ed=[{title:"Dashboard",href:"/dashboard",icon:Pa}],td=[{title:"Repository",href:"https://github.com/laravel/react-starter-kit",icon:Ma},{title:"Documentation",href:"https://laravel.com/docs/starter-kits#react",icon:ba}];function nd(){return s.jsxs(Uc,{collapsible:"icon",variant:"inset",children:[s.jsx(Yc,{children:s.jsx(Ct,{children:s.jsx(Et,{children:s.jsx(Rt,{size:"lg",asChild:!0,children:s.jsx(Fe,{href:"/dashboard",prefetch:!0,children:s.jsx(Ju,{})})})})})}),s.jsx(qc,{children:s.jsx(ol,{items:ed})}),s.jsxs(Xc,{children:[s.jsx(rl,{items:td,className:"mt-auto"}),s.jsx(Qu,{})]})]})}function rd({...e}){return s.jsx("nav",{"aria-label":"breadcrumb","data-slot":"breadcrumb",...e})}function od({className:e,...t}){return s.jsx("ol",{"data-slot":"breadcrumb-list",className:T("text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5",e),...t})}function ad({className:e,...t}){return s.jsx("li",{"data-slot":"breadcrumb-item",className:T("inline-flex items-center gap-1.5",e),...t})}function sd({asChild:e,className:t,...n}){const r=e?ve:"a";return s.jsx(r,{"data-slot":"breadcrumb-link",className:T("hover:text-foreground transition-colors",t),...n})}function id({className:e,...t}){return s.jsx("span",{"data-slot":"breadcrumb-page",role:"link","aria-disabled":"true","aria-current":"page",className:T("text-foreground font-normal",e),...t})}function cd({children:e,className:t,...n}){return s.jsx("li",{"data-slot":"breadcrumb-separator",role:"presentation","aria-hidden":"true",className:T("[&>svg]:size-3.5",t),...n,children:e??s.jsx(Ca,{})})}function ld({breadcrumbs:e}){return s.jsx(s.Fragment,{children:e.length>0&&s.jsx(rd,{children:s.jsx(od,{children:e.map((t,n)=>{const r=n===e.length-1;return s.jsxs(l.Fragment,{children:[s.jsx(ad,{children:r?s.jsx(id,{children:t.title}):s.jsx(sd,{asChild:!0,children:s.jsx(Fe,{href:t.href,children:t.title})})}),!r&&s.jsx(cd,{})]},n)})})})})}function ud({breadcrumbs:e=[]}){return s.jsx("header",{className:"border-sidebar-border/50 flex h-16 shrink-0 items-center gap-2 border-b px-6 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12 md:px-4",children:s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx(Vc,{className:"-ml-1"}),s.jsx(ld,{breadcrumbs:e})]})})}function dd({children:e,breadcrumbs:t=[]}){return s.jsxs(tl,{variant:"sidebar",children:[s.jsx(nd,{}),s.jsxs(el,{variant:"sidebar",children:[s.jsx(ud,{breadcrumbs:t}),e]})]})}const Ed=({children:e,breadcrumbs:t,...n})=>s.jsx(dd,{breadcrumbs:t,...n,children:e});export{Ed as A,Zs as C,Js as D,qs as O,Xs as P,Ys as R,xd as T,ka as X,ei as a,Qs as b};
